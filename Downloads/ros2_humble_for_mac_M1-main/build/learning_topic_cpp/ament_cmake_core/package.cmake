set(_AMENT_PACKAGE_NAME "learning_topic_cpp")
set(learning_topic_cpp_VERSION "0.0.0")
set(learning_topic_cpp_MAINTAINER "root <<EMAIL>>")
set(learning_topic_cpp_BUILD_DEPENDS "rclcpp" "std_msgs")
set(learning_topic_cpp_BUILDTOOL_DEPENDS "ament_cmake")
set(learning_topic_cpp_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs")
set(learning_topic_cpp_BUILDTOOL_EXPORT_DEPENDS )
set(learning_topic_cpp_EXEC_DEPENDS "rclcpp" "std_msgs")
set(learning_topic_cpp_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(learning_topic_cpp_GROUP_DEPENDS )
set(learning_topic_cpp_MEMBER_OF_GROUPS )
set(learning_topic_cpp_DEPRECATED "")
set(learning_topic_cpp_EXPORT_TAGS)
list(APPEND learning_topic_cpp_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
