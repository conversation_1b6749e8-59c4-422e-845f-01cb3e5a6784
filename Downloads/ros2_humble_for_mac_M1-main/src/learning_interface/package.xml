<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>learning_interface</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">jiafeng</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>geometry_msgs</depend>
  
  <depend>rclcpp</depend>
  <depend>learning_interface</depend>
  
  <buildtool_depend>rosidl_defalut_generators</buildtool_depend>
  <exec_depend>rosidl_defalut_runtime</exec_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
