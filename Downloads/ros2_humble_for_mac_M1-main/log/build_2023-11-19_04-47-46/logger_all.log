[0.667s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'learning_topic_cpp']
[0.668s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=5, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['learning_topic_cpp'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffb7bbc040>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffb7bbd3c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffb7bbd3c0>>, mixin_verb=('build',))
[0.808s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.811s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/Gazebo.meta'
[0.813s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/fastrtps.meta'
[0.828s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.830s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.831s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.832s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.834s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.835s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros'
[0.836s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.837s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.840s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.846s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.847s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.847s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.848s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.859s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.862s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.865s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.868s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ignore', 'ignore_ament_install']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore_ament_install'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_pkg']
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_pkg'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_meta']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_meta'
[0.926s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ros']
[0.927s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ros'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['cmake', 'python']
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'cmake'
[0.937s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python'
[0.940s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['python_setup_py']
[0.941s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python_setup_py'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.952s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.953s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.954s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ignore', 'ignore_ament_install']
[0.955s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore'
[0.970s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore_ament_install'
[0.973s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_pkg']
[0.974s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_pkg'
[0.976s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_meta']
[0.977s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_meta'
[0.992s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ros']
[0.995s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ros'
[1.000s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['cmake', 'python']
[1.001s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'cmake'
[1.005s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python'
[1.007s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['python_setup_py']
[1.007s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python_setup_py'
[1.012s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.014s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.026s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ignore', 'ignore_ament_install']
[1.030s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore'
[1.032s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore_ament_install'
[1.037s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_pkg']
[1.039s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_pkg'
[1.041s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_meta']
[1.042s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_meta'
[1.043s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ros']
[1.044s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ros'
[1.092s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['cmake', 'python']
[1.093s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'cmake'
[1.095s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['python_setup_py']
[1.099s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python_setup_py'
[1.102s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ignore', 'ignore_ament_install']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore'
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore_ament_install'
[1.112s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_pkg']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_pkg'
[1.115s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_meta']
[1.116s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_meta'
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ros']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ros'
[1.137s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['cmake', 'python']
[1.138s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'cmake'
[1.140s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['python_setup_py']
[1.156s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python_setup_py'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.171s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.177s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ignore', 'ignore_ament_install']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore_ament_install'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_pkg']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_pkg'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_meta']
[1.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_meta'
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ros']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ros'
[1.254s] DEBUG:colcon.colcon_core.package_identification:Found ROS schema reference in package manifest in 'src/learning_interface'
[1.258s] WARNING:colcon.colcon_core.package_identification:Failed to parse ROS package manifest in 'src/learning_interface': Error(s) in package 'src/learning_interface/package.xml':
The package "learning_interface" must not "build_depend" on a package with the same name as this package
The package "learning_interface" must not "build_export_depend" on a package with the same name as this package
The package "learning_interface" must not "exec_depend" on a package with the same name as this package
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['cmake', 'python']
[1.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'cmake'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'python'
[1.275s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_interface' with type 'cmake' and name 'learning_interface'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore'
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore_ament_install'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_pkg']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_pkg'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_meta']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_meta'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ros']
[1.305s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ros'
[1.323s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp' with type 'ros.ament_cmake' and name 'learning_pkg_cpp'
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ignore', 'ignore_ament_install']
[1.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore'
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore_ament_install'
[1.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_pkg']
[1.340s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_pkg'
[1.342s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_meta']
[1.344s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_meta'
[1.345s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ros']
[1.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ros'
[1.354s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp_sub' with type 'ros.ament_cmake' and name 'learning_pkg_cpp_sub'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore'
[1.358s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore_ament_install'
[1.360s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_pkg']
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_pkg'
[1.366s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_meta']
[1.367s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_meta'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ros']
[1.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ros'
[1.418s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_topic_cpp' with type 'ros.ament_cmake' and name 'learning_topic_cpp'
[1.420s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.422s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.423s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.424s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.426s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.471s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_interface' in 'src/learning_interface'
[1.478s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp' in 'src/learning_pkg_cpp'
[1.479s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp_sub' in 'src/learning_pkg_cpp_sub'
[1.487s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_args' from command line to 'None'
[1.488s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target' from command line to 'None'
[1.490s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.490s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_cache' from command line to 'False'
[1.491s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_first' from command line to 'False'
[1.492s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_force_configure' from command line to 'False'
[1.505s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'ament_cmake_args' from command line to 'None'
[1.507s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_cmake_args' from command line to 'None'
[1.508s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.510s] DEBUG:colcon.colcon_core.verb:Building package 'learning_topic_cpp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros/build/learning_topic_cpp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros/install/learning_topic_cpp', 'merge_install': False, 'path': '/home/<USER>/ros/src/learning_topic_cpp', 'symlink_install': False, 'test_result_base': None}
[1.518s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.526s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.534s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros/src/learning_topic_cpp' with build type 'ament_cmake'
[1.535s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros/src/learning_topic_cpp'
[1.554s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.556s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.557s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.728s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros/src/learning_topic_cpp -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros/install/learning_topic_cpp
[11.709s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros/src/learning_topic_cpp -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros/install/learning_topic_cpp
[11.723s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[18.189s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[18.252s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[19.972s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[19.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[20.024s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[20.072s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[20.108s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[20.118s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[20.130s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[20.143s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[20.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[20.200s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[20.204s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[20.208s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[20.212s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[20.223s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[20.238s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[20.264s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[20.285s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[20.297s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[20.304s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[20.361s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[20.369s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[20.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[20.486s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[20.495s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[20.503s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[20.510s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[20.521s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[20.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[20.527s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[20.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[20.530s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[20.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[20.598s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[20.606s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[20.618s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[20.638s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[20.646s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[20.665s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[20.667s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[20.670s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[20.673s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[20.698s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Could not find 'notify-send'
[20.699s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[20.701s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[20.702s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[20.706s] DEBUG:colcon.colcon_notification.desktop_notification.notify2:Failed to initialize notify2: org.freedesktop.DBus.Error.Spawn.ExecFailed: /usr/bin/dbus-launch terminated abnormally without any error message
[20.708s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[20.718s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.ps1'
[20.730s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_ps1.py'
[20.784s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.ps1'
[20.800s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.sh'
[20.814s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_sh.py'
[20.839s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.sh'
[20.858s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.bash'
[20.869s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.bash'
[20.884s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.zsh'
[20.894s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.zsh'
