[0.179s] Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros/src/learning_topic_cpp -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros/install/learning_topic_cpp
[0.918s] -- The C compiler identification is GNU 11.4.0
[1.499s] -- The CXX compiler identification is GNU 11.4.0
[1.594s] -- Detecting C compiler ABI info
[2.949s] -- Detecting C compiler ABI info - done
[2.975s] -- Check for working C compiler: /usr/bin/cc - skipped
[2.980s] -- Detecting C compile features
[3.030s] -- Detecting C compile features - done
[3.079s] -- Detecting CXX compiler ABI info
[4.342s] -- Detecting CXX compiler ABI info - done
[4.405s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[4.410s] -- Detecting CXX compile features
[4.419s] -- Detecting CXX compile features - done
[4.486s] -- Found ament_cmake: 1.3.5 (/opt/ros/humble/share/ament_cmake/cmake)
[4.690s] -- Found Python3: /usr/bin/python3.10 (found version "3.10.12") found components: Interpreter 
[5.003s] -- Found rclcpp: 16.0.6 (/opt/ros/humble/share/rclcpp/cmake)
[5.057s] -- Found rosidl_generator_c: 3.1.5 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[5.065s] -- Found rosidl_adapter: 3.1.5 (/opt/ros/humble/share/rosidl_adapter/cmake)
[5.077s] -- Found rosidl_generator_cpp: 3.1.5 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[5.129s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[5.160s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[5.241s] -- Found rmw_implementation_cmake: 6.1.1 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[5.257s] -- Found rmw_fastrtps_cpp: 6.2.4 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[5.365s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")  
[5.414s] -- Found FastRTPS: /opt/ros/humble/include  
[5.515s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[5.594s] -- Looking for pthread.h
[6.755s] -- Looking for pthread.h - found
[6.765s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[7.938s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[7.943s] -- Found Threads: TRUE  
[8.088s] -- Found ament_lint_auto: 0.12.8 (/opt/ros/humble/share/ament_lint_auto/cmake)
[8.717s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[8.723s] -- Configured cppcheck include dirs: 
[8.733s] -- Configured cppcheck exclude dirs and/or files: 
[8.739s] -- Added test 'lint_cmake' to check CMake code style
[8.758s] -- Added test 'uncrustify' to check C / C++ code style
[8.775s] -- Configured uncrustify additional arguments: 
[8.779s] -- Added test 'xmllint' to check XML markup files
[8.984s] -- Configuring done
[9.911s] -- Generating done
[10.151s] -- Build files have been written to: /home/<USER>/ros/build/learning_topic_cpp
[10.161s] Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros/src/learning_topic_cpp -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros/install/learning_topic_cpp
[10.175s] Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[10.423s] [ 50%] [32mBuilding CXX object CMakeFiles/talker_pop.dir/src/pub_topic_pop.cpp.o[0m
[14.085s] [100%] [32m[1mLinking CXX executable talker_pop[0m
[16.593s] [100%] Built target talker_pop
[16.635s] Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[16.699s] Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[16.710s] -- Install configuration: ""
[16.775s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/lib/learning_topic_cpp/talker_pop
[17.413s] -- Set runtime path of "/home/<USER>/ros/install/learning_topic_cpp/lib/learning_topic_cpp/talker_pop" to ""
[17.484s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/package_run_dependencies/learning_topic_cpp
[17.553s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/parent_prefix_path/learning_topic_cpp
[17.640s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/ament_prefix_path.sh
[17.703s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/ament_prefix_path.dsv
[17.739s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/path.sh
[17.810s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/path.dsv
[17.867s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.bash
[17.897s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.sh
[17.979s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.zsh
[18.040s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.dsv
[18.073s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv
[18.190s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/packages/learning_topic_cpp
[18.259s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/cmake/learning_topic_cppConfig.cmake
[18.349s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/cmake/learning_topic_cppConfig-version.cmake
[18.392s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.xml
[18.470s] Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
