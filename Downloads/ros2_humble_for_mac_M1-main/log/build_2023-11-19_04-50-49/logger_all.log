[0.572s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'learning_topic_cpp']
[0.586s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=5, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['learning_topic_cpp'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff97b38070>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff97b38fd0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff97b38fd0>>, mixin_verb=('build',))
[0.617s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.619s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/Gazebo.meta'
[0.630s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/fastrtps.meta'
[0.633s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.634s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.636s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.637s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.638s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.640s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros'
[0.642s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.646s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.649s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.652s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.653s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.655s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.656s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.657s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.658s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.672s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.673s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.674s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.677s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.677s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ignore', 'ignore_ament_install']
[0.685s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore'
[0.687s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore_ament_install'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_pkg']
[0.734s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_pkg'
[0.737s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_meta']
[0.738s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_meta'
[0.739s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ros']
[0.741s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ros'
[0.749s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['cmake', 'python']
[0.750s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'cmake'
[0.753s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python'
[0.760s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['python_setup_py']
[0.761s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python_setup_py'
[0.763s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.764s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.765s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.766s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ignore', 'ignore_ament_install']
[0.768s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore'
[0.782s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore_ament_install'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_pkg']
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_pkg'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_meta']
[0.801s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_meta'
[0.803s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ros']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ros'
[0.809s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['cmake', 'python']
[0.813s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'cmake'
[0.815s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python'
[0.817s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['python_setup_py']
[0.818s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python_setup_py'
[0.820s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.821s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.822s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.831s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.832s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ignore', 'ignore_ament_install']
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore'
[0.837s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore_ament_install'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_pkg']
[0.847s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_pkg'
[0.850s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_meta']
[0.850s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_meta'
[0.851s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ros']
[0.852s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ros'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['cmake', 'python']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'cmake'
[0.906s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python'
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['python_setup_py']
[0.910s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python_setup_py'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ignore', 'ignore_ament_install']
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore_ament_install'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_pkg']
[0.925s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_pkg'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_meta']
[0.929s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_meta'
[0.943s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ros']
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ros'
[0.952s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['cmake', 'python']
[0.953s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'cmake'
[0.956s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python'
[0.970s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['python_setup_py']
[0.971s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python_setup_py'
[0.974s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.975s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.978s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.983s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.985s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.987s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.992s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.998s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.999s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.002s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.006s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.008s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.011s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ignore', 'ignore_ament_install']
[1.012s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore'
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore_ament_install'
[1.019s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_pkg']
[1.020s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_pkg'
[1.022s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_meta']
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_meta'
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ros']
[1.026s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ros'
[1.076s] DEBUG:colcon.colcon_core.package_identification:Found ROS schema reference in package manifest in 'src/learning_interface'
[1.078s] WARNING:colcon.colcon_core.package_identification:Failed to parse ROS package manifest in 'src/learning_interface': Error(s) in package 'src/learning_interface/package.xml':
The package "learning_interface" must not "build_depend" on a package with the same name as this package
The package "learning_interface" must not "build_export_depend" on a package with the same name as this package
The package "learning_interface" must not "exec_depend" on a package with the same name as this package
[1.081s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['cmake', 'python']
[1.082s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'cmake'
[1.087s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'python'
[1.095s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_interface' with type 'cmake' and name 'learning_interface'
[1.096s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.097s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore'
[1.099s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore_ament_install'
[1.102s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_pkg']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_pkg'
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_meta']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_meta'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ros']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ros'
[1.146s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp' with type 'ros.ament_cmake' and name 'learning_pkg_cpp'
[1.147s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ignore', 'ignore_ament_install']
[1.149s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore_ament_install'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_pkg']
[1.164s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_pkg'
[1.167s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_meta']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_meta'
[1.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ros']
[1.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ros'
[1.174s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp_sub' with type 'ros.ament_cmake' and name 'learning_pkg_cpp_sub'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore_ament_install'
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_pkg']
[1.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_pkg'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_meta']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_meta'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ros']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ros'
[1.199s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_topic_cpp' with type 'ros.ament_cmake' and name 'learning_topic_cpp'
[1.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.245s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.285s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_interface' in 'src/learning_interface'
[1.286s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp' in 'src/learning_pkg_cpp'
[1.288s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp_sub' in 'src/learning_pkg_cpp_sub'
[1.301s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_args' from command line to 'None'
[1.303s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target' from command line to 'None'
[1.303s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.304s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_cache' from command line to 'False'
[1.305s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_first' from command line to 'False'
[1.307s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_force_configure' from command line to 'False'
[1.307s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'ament_cmake_args' from command line to 'None'
[1.321s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_cmake_args' from command line to 'None'
[1.322s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.324s] DEBUG:colcon.colcon_core.verb:Building package 'learning_topic_cpp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros/build/learning_topic_cpp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros/install/learning_topic_cpp', 'merge_install': False, 'path': '/home/<USER>/ros/src/learning_topic_cpp', 'symlink_install': False, 'test_result_base': None}
[1.331s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.339s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.352s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros/src/learning_topic_cpp' with build type 'ament_cmake'
[1.353s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros/src/learning_topic_cpp'
[1.366s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.367s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.368s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.540s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[13.014s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[13.067s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[14.041s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[14.068s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[14.114s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[14.143s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[14.161s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[14.171s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[14.178s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[14.201s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[14.211s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[14.212s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.214s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[14.217s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[14.220s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.229s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[14.239s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[14.248s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[14.255s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[14.307s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[14.314s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[14.374s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[14.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[14.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[14.419s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[14.427s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[14.437s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[14.444s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[14.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[14.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[14.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[14.504s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.513s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[14.526s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[14.535s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[14.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[14.573s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[14.585s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[14.592s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[14.594s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[14.595s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[14.597s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[14.627s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Could not find 'notify-send'
[14.629s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[14.630s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[14.633s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[14.637s] DEBUG:colcon.colcon_notification.desktop_notification.notify2:Failed to initialize notify2: org.freedesktop.DBus.Error.Spawn.ExecFailed: /usr/bin/dbus-launch terminated abnormally without any error message
[14.639s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[14.648s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.ps1'
[14.695s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_ps1.py'
[14.709s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.ps1'
[14.739s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.sh'
[14.756s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_sh.py'
[14.767s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.sh'
[14.785s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.bash'
[14.793s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.bash'
[14.812s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.zsh'
[14.820s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.zsh'
