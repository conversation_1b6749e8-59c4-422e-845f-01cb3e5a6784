[0.522s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'learning_topic_cpp']
[0.523s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=5, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['learning_topic_cpp'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffa136c070>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffa136cfd0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffa136cfd0>>, mixin_verb=('build',))
[0.594s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.596s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/Gazebo.meta'
[0.598s] INFO:colcon.colcon_metadata.package_discovery.colcon_meta:Using configuration from '/root/.colcon/metadata/default/fastrtps.meta'
[0.601s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.602s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.603s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.605s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.612s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.613s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros'
[0.615s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.616s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.619s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.633s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.635s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.637s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.638s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.639s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.640s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.666s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.670s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.676s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.677s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ignore', 'ignore_ament_install']
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore'
[0.685s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ignore_ament_install'
[0.688s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_pkg']
[0.689s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_pkg'
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['colcon_meta']
[0.691s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'colcon_meta'
[0.694s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['ros']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'ros'
[0.701s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['cmake', 'python']
[0.702s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'cmake'
[0.706s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python'
[0.708s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extensions ['python_setup_py']
[0.709s] Level 1:colcon.colcon_core.package_identification:_identify(auto) by extension 'python_setup_py'
[0.712s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.713s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.714s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.716s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ignore', 'ignore_ament_install']
[0.717s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore'
[0.762s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ignore_ament_install'
[0.767s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_pkg']
[0.768s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_pkg'
[0.771s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['colcon_meta']
[0.772s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'colcon_meta'
[0.774s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['ros']
[0.775s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'ros'
[0.785s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['cmake', 'python']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'cmake'
[0.789s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python'
[0.790s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extensions ['python_setup_py']
[0.804s] Level 1:colcon.colcon_core.package_identification:_identify(doc) by extension 'python_setup_py'
[0.807s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.808s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.810s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.831s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ignore', 'ignore_ament_install']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore'
[0.836s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ignore_ament_install'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_pkg']
[0.846s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_pkg'
[0.849s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['colcon_meta']
[0.850s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'colcon_meta'
[0.851s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['ros']
[0.852s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'ros'
[0.859s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['cmake', 'python']
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'cmake'
[0.864s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python'
[0.866s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extensions ['python_setup_py']
[0.867s] Level 1:colcon.colcon_core.package_identification:_identify(resource) by extension 'python_setup_py'
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ignore', 'ignore_ament_install']
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ignore_ament_install'
[0.876s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_pkg']
[0.877s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_pkg'
[0.879s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['colcon_meta']
[0.880s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'colcon_meta'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['ros']
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'ros'
[0.931s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['cmake', 'python']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'cmake'
[0.935s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python'
[0.937s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extensions ['python_setup_py']
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(resource/vscode_plug) by extension 'python_setup_py'
[0.942s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.943s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.952s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.955s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.956s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.958s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.973s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.974s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.981s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.996s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.998s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.001s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.003s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ignore', 'ignore_ament_install']
[1.007s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore'
[1.010s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ignore_ament_install'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_pkg']
[1.014s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_pkg'
[1.016s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['colcon_meta']
[1.017s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'colcon_meta'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['ros']
[1.019s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'ros'
[1.026s] DEBUG:colcon.colcon_core.package_identification:Found ROS schema reference in package manifest in 'src/learning_interface'
[1.027s] WARNING:colcon.colcon_core.package_identification:Failed to parse ROS package manifest in 'src/learning_interface': Error(s) in package 'src/learning_interface/package.xml':
The package "learning_interface" must not "build_depend" on a package with the same name as this package
The package "learning_interface" must not "build_export_depend" on a package with the same name as this package
The package "learning_interface" must not "exec_depend" on a package with the same name as this package
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extensions ['cmake', 'python']
[1.030s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'cmake'
[1.036s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_interface) by extension 'python'
[1.038s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_interface' with type 'cmake' and name 'learning_interface'
[1.040s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.041s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore'
[1.045s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ignore_ament_install'
[1.096s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_pkg']
[1.097s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_pkg'
[1.100s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['colcon_meta']
[1.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'colcon_meta'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extensions ['ros']
[1.104s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp) by extension 'ros'
[1.115s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp' with type 'ros.ament_cmake' and name 'learning_pkg_cpp'
[1.116s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ignore', 'ignore_ament_install']
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore'
[1.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ignore_ament_install'
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_pkg']
[1.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_pkg'
[1.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['colcon_meta']
[1.140s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'colcon_meta'
[1.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extensions ['ros']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_pkg_cpp_sub) by extension 'ros'
[1.159s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_pkg_cpp_sub' with type 'ros.ament_cmake' and name 'learning_pkg_cpp_sub'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ignore', 'ignore_ament_install']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore'
[1.164s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ignore_ament_install'
[1.169s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_pkg']
[1.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_pkg'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['colcon_meta']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'colcon_meta'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extensions ['ros']
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/learning_topic_cpp) by extension 'ros'
[1.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learning_topic_cpp' with type 'ros.ament_cmake' and name 'learning_topic_cpp'
[1.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.184s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.186s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.223s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_interface' in 'src/learning_interface'
[1.224s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp' in 'src/learning_pkg_cpp'
[1.225s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learning_pkg_cpp_sub' in 'src/learning_pkg_cpp_sub'
[1.234s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_args' from command line to 'None'
[1.235s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target' from command line to 'None'
[1.236s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.237s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_cache' from command line to 'False'
[1.238s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_clean_first' from command line to 'False'
[1.239s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'cmake_force_configure' from command line to 'False'
[1.240s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'ament_cmake_args' from command line to 'None'
[1.283s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_cmake_args' from command line to 'None'
[1.284s] Level 5:colcon.colcon_core.verb:set package 'learning_topic_cpp' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.288s] DEBUG:colcon.colcon_core.verb:Building package 'learning_topic_cpp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros/build/learning_topic_cpp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros/install/learning_topic_cpp', 'merge_install': False, 'path': '/home/<USER>/ros/src/learning_topic_cpp', 'symlink_install': False, 'test_result_base': None}
[1.296s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.304s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.305s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros/src/learning_topic_cpp' with build type 'ament_cmake'
[1.306s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros/src/learning_topic_cpp'
[1.315s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.316s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.318s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[13.053s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[13.121s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[13.980s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[13.993s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[14.084s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[14.103s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[14.110s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[14.118s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[14.130s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[14.136s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[14.145s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[14.147s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.150s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[14.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[14.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.166s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[14.217s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[14.226s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[14.248s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[14.258s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[14.280s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[14.347s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learning_topic_cpp)
[14.357s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake module files
[14.381s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp' for CMake config files
[14.387s] Level 1:colcon.colcon_core.shell:create_environment_hook('learning_topic_cpp', 'cmake_prefix_path')
[14.397s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.ps1'
[14.403s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.dsv'
[14.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/hook/cmake_prefix_path.sh'
[14.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib'
[14.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/pkgconfig/learning_topic_cpp.pc'
[14.476s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/lib/python3.10/site-packages'
[14.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros/install/learning_topic_cpp/bin'
[14.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.ps1'
[14.508s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv'
[14.528s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.sh'
[14.538s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.bash'
[14.545s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.zsh'
[14.551s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros/install/learning_topic_cpp/share/colcon-core/packages/learning_topic_cpp)
[14.559s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[14.562s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[14.565s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[14.566s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[14.635s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Could not find 'notify-send'
[14.636s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[14.638s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[14.640s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[14.643s] DEBUG:colcon.colcon_notification.desktop_notification.notify2:Failed to initialize notify2: org.freedesktop.DBus.Error.Spawn.ExecFailed: /usr/bin/dbus-launch terminated abnormally without any error message
[14.644s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[14.660s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.ps1'
[14.668s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_ps1.py'
[14.688s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.ps1'
[14.707s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.sh'
[14.717s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros/install/_local_setup_util_sh.py'
[14.723s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.sh'
[14.741s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.bash'
[14.750s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.bash'
[14.809s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros/install/local_setup.zsh'
[14.817s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros/install/setup.zsh'
