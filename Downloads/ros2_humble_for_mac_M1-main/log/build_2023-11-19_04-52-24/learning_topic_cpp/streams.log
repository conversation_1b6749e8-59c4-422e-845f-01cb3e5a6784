[0.161s] Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[0.266s] [35m[1mConsolidate compiler generated dependencies of target talker_pop[0m
[5.023s] [ 50%] [32mBuilding CXX object CMakeFiles/talker_pop.dir/src/pub_topic_pop.cpp.o[0m
[9.116s] [100%] [32m[1mLinking CXX executable talker_pop[0m
[11.686s] [100%] Built target talker_pop
[11.719s] Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros/build/learning_topic_cpp -- -j5 -l5
[11.786s] Invoking command in '/home/<USER>/ros/build/learning_topic_cpp': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
[11.797s] -- Install configuration: ""
[11.806s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/lib/learning_topic_cpp/talker_pop
[12.543s] -- Set runtime path of "/home/<USER>/ros/install/learning_topic_cpp/lib/learning_topic_cpp/talker_pop" to ""
[12.551s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/package_run_dependencies/learning_topic_cpp
[12.563s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/parent_prefix_path/learning_topic_cpp
[12.569s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/ament_prefix_path.sh
[12.587s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/ament_prefix_path.dsv
[12.605s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/path.sh
[12.611s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/environment/path.dsv
[12.618s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.bash
[12.624s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.sh
[12.630s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.zsh
[12.639s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/local_setup.dsv
[12.647s] -- Installing: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.dsv
[12.657s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/ament_index/resource_index/packages/learning_topic_cpp
[12.712s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/cmake/learning_topic_cppConfig.cmake
[12.721s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/cmake/learning_topic_cppConfig-version.cmake
[12.732s] -- Up-to-date: /home/<USER>/ros/install/learning_topic_cpp/share/learning_topic_cpp/package.xml
[12.736s] Invoked command in '/home/<USER>/ros/build/learning_topic_cpp' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros/build/learning_topic_cpp
