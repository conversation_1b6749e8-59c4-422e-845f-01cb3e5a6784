# docker-compose.yml
# 
# NOTE - Only necessary for machines without an Nvidia GPU
# This Docker Compose configuration sets up a Drake ROS integration environment
# along with a noVNC server for remote access to the graphical interface of any
# visualization tools used in the project.
#
# Author: <PERSON><PERSON><PERSON> <PERSON>

version: '3.8'
services:
  ros_dev_env:
    # container_name: ${ROS_DEV_CONTAINER_NAME}
    # image: ${ROS_DEV_CONTAINER_NAME} ## For now, the names are the same. Can change if ever a need arises.
    container_name: ros2_humble_dev
    image: jiaxiaofeng/ros2_humble_mac_m1:1.0
    build: ./
    volumes: 
      - ./:/home/<USER>/ros 
    environment:
      - DISPLAY=novnc:0.0
      - ROS_DOMAIN_ID=55
      - --privileged=true
    networks:
      - x11
    stdin_open: true
    tty: true
  novnc:
    image: theasp/novnc:latest
    environment:
      - DISPLAY_WIDTH=2560
      - DISPLAY_HEIGHT=1440
    ports:
      - "8080:8080"
    networks:
      - x11
    restart: on-failure
networks:
  x11: 
